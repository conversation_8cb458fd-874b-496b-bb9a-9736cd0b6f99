import { invoke } from '@tauri-apps/api/core';
import {
  OutfitImageRecord,
  OutfitImageGenerationRequest,
  OutfitImageGenerationResponse,
  OutfitImageStats,
  ModelDashboardStats
} from '../types/outfitImage';

/**
 * 穿搭图片服务
 * 遵循 Tauri 开发规范的服务层设计原则
 */
export class OutfitImageService {
  /**
   * 获取模特个人看板统计信息
   */
  static async getModelDashboardStats(modelId: string): Promise<ModelDashboardStats> {
    try {
      console.log('📊 获取模特个人看板统计信息:', modelId);
      
      const stats = await invoke<ModelDashboardStats>('get_model_dashboard_stats', {
        modelId
      });
      
      console.log('✅ 模特个人看板统计信息获取成功:', stats);
      return stats;
    } catch (error) {
      console.error('❌ 获取模特个人看板统计信息失败:', error);
      throw new Error(`获取模特个人看板统计信息失败: ${error}`);
    }
  }

  /**
   * 获取模特的穿搭图片生成记录列表
   */
  static async getOutfitImageRecords(modelId: string): Promise<OutfitImageRecord[]> {
    try {
      console.log('📋 获取模特穿搭图片生成记录:', modelId);
      
      const records = await invoke<OutfitImageRecord[]>('get_outfit_image_records', {
        modelId
      });
      
      console.log('✅ 获取到穿搭图片记录:', records.length, '条');
      return records;
    } catch (error) {
      console.error('❌ 获取穿搭图片记录失败:', error);
      throw new Error(`获取穿搭图片记录失败: ${error}`);
    }
  }

  /**
   * 创建穿搭图片生成记录
   */
  static async createOutfitImageRecord(request: OutfitImageGenerationRequest): Promise<string> {
    try {
      console.log('🎨 创建穿搭图片生成记录:', request);
      
      const recordId = await invoke<string>('create_outfit_image_record', {
        request
      });
      
      console.log('✅ 穿搭图片生成记录创建成功:', recordId);
      return recordId;
    } catch (error) {
      console.error('❌ 创建穿搭图片生成记录失败:', error);
      throw new Error(`创建穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 删除穿搭图片生成记录
   */
  static async deleteOutfitImageRecord(recordId: string): Promise<void> {
    try {
      console.log('🗑️ 删除穿搭图片生成记录:', recordId);
      
      await invoke<void>('delete_outfit_image_record', {
        recordId
      });
      
      console.log('✅ 穿搭图片生成记录删除成功');
    } catch (error) {
      console.error('❌ 删除穿搭图片生成记录失败:', error);
      throw new Error(`删除穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 获取穿搭图片生成记录详情
   */
  static async getOutfitImageRecordDetail(recordId: string): Promise<OutfitImageRecord | null> {
    try {
      console.log('🔍 获取穿搭图片生成记录详情:', recordId);
      
      const record = await invoke<OutfitImageRecord | null>('get_outfit_image_record_detail', {
        recordId
      });
      
      console.log('✅ 穿搭图片生成记录详情获取成功');
      return record;
    } catch (error) {
      console.error('❌ 获取穿搭图片生成记录详情失败:', error);
      throw new Error(`获取穿搭图片生成记录详情失败: ${error}`);
    }
  }

  /**
   * 生成穿搭图片
   */
  static async generateOutfitImages(request: OutfitImageGenerationRequest): Promise<OutfitImageGenerationResponse> {
    try {
      console.log('🎨 开始生成穿搭图片:', request);

      // 调用后端API执行实际的AI生成逻辑
      const response = await invoke<OutfitImageGenerationResponse>('execute_outfit_image_generation', {
        request
      });

      console.log('✅ 穿搭图片生成完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 穿搭图片生成失败:', error);
      throw new Error(`穿搭图片生成失败: ${error}`);
    }
  }

  /**
   * 批量删除穿搭图片生成记录
   */
  static async batchDeleteOutfitImageRecords(recordIds: string[]): Promise<void> {
    try {
      console.log('🗑️ 批量删除穿搭图片生成记录:', recordIds.length, '条');
      
      const deletePromises = recordIds.map(recordId => 
        this.deleteOutfitImageRecord(recordId)
      );
      
      await Promise.all(deletePromises);
      
      console.log('✅ 批量删除穿搭图片生成记录成功');
    } catch (error) {
      console.error('❌ 批量删除穿搭图片生成记录失败:', error);
      throw new Error(`批量删除穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 获取模特的穿搭图片统计信息（从看板统计中提取）
   */
  static async getOutfitImageStats(modelId: string): Promise<OutfitImageStats> {
    try {
      const dashboardStats = await this.getModelDashboardStats(modelId);
      return dashboardStats.outfit_stats;
    } catch (error) {
      console.error('❌ 获取穿搭图片统计信息失败:', error);
      throw new Error(`获取穿搭图片统计信息失败: ${error}`);
    }
  }

  /**
   * 检查模特是否有穿搭图片记录
   */
  static async hasOutfitImageRecords(modelId: string): Promise<boolean> {
    try {
      const records = await this.getOutfitImageRecords(modelId);
      return records.length > 0;
    } catch (error) {
      console.error('❌ 检查穿搭图片记录失败:', error);
      return false;
    }
  }

  /**
   * 获取模特最近的穿搭图片生成记录
   */
  static async getRecentOutfitImageRecords(modelId: string, limit: number = 5): Promise<OutfitImageRecord[]> {
    try {
      const records = await this.getOutfitImageRecords(modelId);
      return records.slice(0, limit);
    } catch (error) {
      console.error('❌ 获取最近穿搭图片记录失败:', error);
      throw new Error(`获取最近穿搭图片记录失败: ${error}`);
    }
  }
}
